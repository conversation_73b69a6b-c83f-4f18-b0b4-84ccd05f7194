{
  "root": true,
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2020,
    "sourceType": "module", // important for "import" syntax
    "ecmaFeatures": {
      "jsx": true
    },
    "project": "./tsconfig.json"
  },
  "plugins": ["@typescript-eslint", "react", "react-hooks"],
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "prettier"
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    "no-console": "warn",
    "import/first": "off",
    "@typescript-eslint/consistent-type-imports": "off",
    "prefer-const": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "no-var": "off",
    "@typescript-eslint/no-unused-vars": "warn",
    "@typescript-eslint/no-explicit-any": "off",
    "react/react-in-jsx-scope": "off",
    "react/display-name": "off",
    "react/no-unescaped-entities": "off",
    "jsx-a11y/click-events-have-key-events": "off",
    "jsx-a11y/label-has-associated-control": "off",
    "jsx-a11y/no-noninteractive-element-interactions": "off",
    "jsx-a11y/no-autofocus": "off",
    "jsx-a11y/no-static-element-interactions": "off",
    "jsx-a11y/media-has-caption": "off",
    "jsx-a11y/anchor-has-content": "off",
    "jsx-a11y/no-redundant-roles": "off",
    "jsx-a11y/no-noninteractive-tabindex": "off",

    "no-constant-condition": "off",
    "no-prototype-builtins": "off",
    "react/jsx-key": "warn",
    "react/prop-types": "off",
    "react/no-children-prop": "off",
    "@typescript-eslint/no-empty-object-type": "off",
    "@typescript-eslint/no-unused-expressions": "off"
  },
  "overrides": [
    {
      "files": ["**/*.js", "./tailwind.config.ts"],
      "rules": {
        "@typescript-eslint/no-require-imports": "off"
      }
    }
  ]
}
