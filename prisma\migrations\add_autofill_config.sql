-- CreateTable
CREATE TABLE "AutoFillConfig" (
    "id" TEXT NOT NULL,
    "entityName" TEXT NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "triggerField" TEXT NOT NULL,
    "workflowName" TEXT,
    "minDomainLength" INTEGER DEFAULT 3,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,

    CONSTRAINT "AutoFillConfig_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AutoFillFieldMapping" (
    "id" TEXT NOT NULL,
    "configId" TEXT NOT NULL,
    "apiField" TEXT NOT NULL,
    "entityField" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AutoFillFieldMapping_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AutoFillConfig_entityName_key" ON "AutoFillConfig"("entityName");

-- CreateIndex
CREATE INDEX "AutoFillFieldMapping_configId_idx" ON "AutoFillFieldMapping"("configId");

-- AddForeignKey
ALTER TABLE "AutoFillFieldMapping" ADD CONSTRAINT "AutoFillFieldMapping_configId_fkey" FOREIGN KEY ("configId") REFERENCES "AutoFillConfig"("id") ON DELETE CASCADE ON UPDATE CASCADE;
