// themes.css should have 2 classes: .theme-{name} and .dark.theme-{name}
export const defaultTheme = "linkworks";

// this array could be empty, it's only used by ThemeSelector
export const defaultThemes: { name: string; value: string }[] = [
  { name: "Linkworks", value: "linkworks" },
  { name: "Zinc", value: "zinc" },
  { name: "Zinc Scaled", value: "zinc-scaled" },
  { name: "Zinc Mono", value: "zinc-mono" },
  { name: "Blue", value: "blue" },
  { name: "Blue Scaled", value: "blue-scaled" },
  { name: "Blue Mono", value: "blue-mono" },
  { name: "<PERSON>", value: "violet" },
  { name: "Slate", value: "slate" },
  { name: "Stone", value: "stone" },
  { name: "Gray", value: "gray" },
  { name: "Neutral", value: "neutral" },
  { name: "Red", value: "red" },
  { name: "<PERSON>", value: "rose" },
  { name: "Orange", value: "orange" },
  { name: "<PERSON>", value: "green" },
  { name: "Yellow", value: "yellow" },
  { name: "Modern Blue", value: "modern-blue" },
];
