import { CheckCircleIcon, ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";

export interface AutoFillStatusProps {
  status: "idle" | "loading" | "success" | "error";
  message?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export default function AutoFillStatus({ 
  status, 
  message, 
  className,
  size = "sm" 
}: AutoFillStatusProps) {
  if (status === "idle") return null;

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5", 
    lg: "h-6 w-6"
  };

  const textSizeClasses = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  };

  const getIcon = () => {
    switch (status) {
      case "loading":
        return (
          <div className={clsx(
            "animate-spin rounded-full border-b-2 border-blue-600",
            sizeClasses[size]
          )}></div>
        );
      case "success":
        return <CheckCircleIcon className={clsx(sizeClasses[size], "text-green-600")} />;
      case "error":
        return <ExclamationTriangleIcon className={clsx(sizeClasses[size], "text-red-600")} />;
      default:
        return null;
    }
  };

  const getTextColor = () => {
    switch (status) {
      case "loading":
        return "text-blue-600";
      case "success":
        return "text-green-600";
      case "error":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className={clsx("flex items-center space-x-2", className)}>
      {getIcon()}
      {message && (
        <span className={clsx(textSizeClasses[size], getTextColor())}>
          {message}
        </span>
      )}
    </div>
  );
}

// Inline status component that can be added next to form fields
export function AutoFillFieldStatus({ 
  status, 
  fieldName,
  className 
}: { 
  status: AutoFillStatusProps["status"];
  fieldName?: string;
  className?: string;
}) {
  const getMessage = () => {
    switch (status) {
      case "loading":
        return "Enriching data...";
      case "success":
        return fieldName ? `${fieldName} auto-filled` : "Auto-filled";
      case "error":
        return "Auto-fill failed";
      default:
        return "";
    }
  };

  return (
    <AutoFillStatus
      status={status}
      message={getMessage()}
      size="sm"
      className={clsx("ml-2", className)}
    />
  );
}

// Badge component for showing auto-fill status
export function AutoFillBadge({ 
  status,
  count,
  className 
}: { 
  status: "success" | "error";
  count?: number;
  className?: string;
}) {
  const getBadgeColor = () => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 border-green-200";
      case "error":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getText = () => {
    if (count !== undefined) {
      return status === "success" 
        ? `${count} field${count !== 1 ? 's' : ''} auto-filled`
        : `Auto-fill failed`;
    }
    return status === "success" ? "Auto-filled" : "Failed";
  };

  return (
    <span className={clsx(
      "inline-flex items-center px-2 py-1 rounded-md text-xs font-medium border",
      getBadgeColor(),
      className
    )}>
      {status === "success" && <CheckCircleIcon className="h-3 w-3 mr-1" />}
      {status === "error" && <ExclamationTriangleIcon className="h-3 w-3 mr-1" />}
      {getText()}
    </span>
  );
}
