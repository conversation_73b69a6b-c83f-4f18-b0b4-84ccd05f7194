import { useState, useCallback, useRef, useEffect } from "react";
import { useSubmit } from "react-router";

export interface AutoFillConfig {
  enabled: boolean;
  debounceMs?: number;
  minLength?: number;
  apiEndpoint?: string;
  workflowId?: string;
  entityName?: string;
  fieldMappings?: Record<string, string>;
}

export interface EnrichedData {
  name?: string;
  industry?: string;
  description?: string;
  employees?: string;
  revenue?: string;
  founded?: string;
  headquarters?: string;
  phone?: string;
  linkedin?: string;
  twitter?: string;
  [key: string]: any;
}

export interface AutoFillResult {
  success: boolean;
  enrichedData?: EnrichedData;
  error?: string;
  domain?: string;
}

export interface UseAutoFillReturn {
  isLoading: boolean;
  error: string | null;
  lastResult: AutoFillResult | null;
  triggerAutoFill: (domain: string, entityId?: string) => Promise<AutoFillResult | null>;
  clearError: () => void;
  reset: () => void;
}

const DEFAULT_CONFIG: AutoFillConfig = {
  enabled: true,
  debounceMs: 1000,
  minLength: 3,
  apiEndpoint: "/api/workflows/autofill",
  entityName: "Company",
  fieldMappings: {
    name: "name",
    industry: "industry", 
    description: "description",
    employees: "employees",
    revenue: "revenue",
    founded: "founded",
    headquarters: "headquarters",
    phone: "phone",
    linkedin: "linkedin",
    twitter: "twitter"
  }
};

export function useAutoFill(config: Partial<AutoFillConfig> = {}): UseAutoFillReturn {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastResult, setLastResult] = useState<AutoFillResult | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setIsLoading(false);
    setError(null);
    setLastResult(null);
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  const triggerAutoFill = useCallback(async (
    domain: string, 
    entityId?: string
  ): Promise<AutoFillResult | null> => {
    if (!finalConfig.enabled) {
      return null;
    }

    // Validate domain length
    if (domain.length < (finalConfig.minLength || 3)) {
      return null;
    }

    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(finalConfig.apiEndpoint!, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          domain,
          entityId,
          entityName: finalConfig.entityName,
          workflowId: finalConfig.workflowId
        }),
        signal: abortControllerRef.current.signal
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}`);
      }

      const autoFillResult: AutoFillResult = {
        success: result.success,
        enrichedData: result.enrichedData,
        domain: result.domain
      };

      setLastResult(autoFillResult);
      return autoFillResult;

    } catch (err: any) {
      if (err.name === 'AbortError') {
        // Request was cancelled, don't set error
        return null;
      }

      const errorMessage = err.message || "Failed to fetch company data";
      setError(errorMessage);
      
      const errorResult: AutoFillResult = {
        success: false,
        error: errorMessage,
        domain
      };
      
      setLastResult(errorResult);
      return errorResult;
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [finalConfig]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    isLoading,
    error,
    lastResult,
    triggerAutoFill,
    clearError,
    reset
  };
}

// Debounced version of the hook
export function useDebouncedAutoFill(config: Partial<AutoFillConfig> = {}): UseAutoFillReturn & {
  debouncedTrigger: (domain: string, entityId?: string) => void;
} {
  const autoFill = useAutoFill(config);
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const debouncedTrigger = useCallback((domain: string, entityId?: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      autoFill.triggerAutoFill(domain, entityId);
    }, finalConfig.debounceMs);
  }, [autoFill.triggerAutoFill, finalConfig.debounceMs]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    ...autoFill,
    debouncedTrigger
  };
}
