# Auto-Fill Integration Guide

This module provides automatic data enrichment for entity forms based on domain/website input without changing the UI.

## How It Works

1. **Workflow-Based**: Uses the existing workflow engine to call external APIs
2. **Non-Intrusive**: Integrates with existing form submission flow
3. **Configurable**: Field mappings and triggers are configurable per entity
4. **Non-Blocking**: Auto-fill failures don't prevent form submission

## Integration Steps

### 1. Add Auto-Fill Hooks to RowsApi

Add these imports to `app/utils/api/.server/RowsApi.ts`:

```typescript
import AutoFillHooks from "~/modules/autoFill/hooks/AutoFillHooks.server";
```

### 2. Integrate with Row Creation

In the `create` function, add this call after `RowHooks.onBeforeCreate`:

```typescript
// Existing code
await RowHooks.onBeforeCreate({ entity: rowEntity, tenantId, userId, rowValues });

// Add auto-fill hook
await AutoFillHooks.onBeforeRowCreate({
  entity: rowEntity,
  tenantId,
  userId,
  rowValues
});
```

### 3. Integrate with Row Updates

In the `update` function, add this call after `RowHooks.onBeforeUpdate`:

```typescript
// Existing code  
await RowHooks.onBeforeUpdate({ id, item: row!, entity: rowEntity, tenantId, userId, rowValues });

// Add auto-fill hook
await AutoFillHooks.onBeforeRowUpdate({
  entity: rowEntity,
  tenantId,
  userId,
  rowValues,
  existingRowValues: {
    dynamicProperties: row!.values.map(v => ({
      propertyId: v.propertyId,
      textValue: v.textValue,
      numberValue: v.numberValue,
      dateValue: v.dateValue,
      booleanValue: v.booleanValue,
      media: []
    }))
  },
  rowId: id
});
```

## Configuration

### Entity Configuration

Auto-fill configurations are defined in `AutoFillService.server.ts`:

```typescript
const DEFAULT_CONFIGS: Record<string, AutoFillConfig> = {
  Company: {
    enabled: true,
    entityName: "Company",
    triggerField: "website",  // Field that triggers auto-fill
    workflowName: "Auto-Fill Company Data",
    minDomainLength: 3,
    fieldMappings: {
      "companyName": "name",      // API field -> Entity property
      "industry": "industry",
      "description": "description",
      // ... more mappings
    }
  }
};
```

### Workflow Setup

1. Create the "Auto-Fill Company Data" workflow in the admin panel
2. Or use the default template that's automatically created
3. Configure API credentials in workflow variables
4. Set the workflow status to "live"

## API Response Structure

The workflow expects this response structure:

```json
{
  "basicInformation": {
    "company_name": "Company Name",
    "website_url": "https://example.com",
    "industry": "Technology",
    "about": "Company description..."
  },
  "locationInformation": {
    "address": "123 Main St",
    "city": "City",
    "state": "State",
    "country": "Country",
    "primary_phone": "+1234567890"
  },
  "companyInfo": {
    "size_of_company": 100,
    "founding_year": 2020,
    "revenue_range": "$1M-$10M"
  },
  "socialLinks": {
    "linkedin_url": "https://linkedin.com/company/...",
    "twitter_url": "https://twitter.com/..."
  },
  "technographics": ["React", "Node.js", "AWS"]
}
```

## Features

- **Domain Extraction**: Automatically extracts domains from URLs
- **Duplicate Prevention**: Only triggers on new/changed domains
- **Type Handling**: Supports text, number, boolean, and array values
- **Non-Overwriting**: Won't overwrite existing field values
- **Error Handling**: Graceful failure without blocking form submission
- **Logging**: Comprehensive logging for debugging

## Testing

To test auto-fill functionality:

1. Create a Company entity with a "website" field
2. Ensure the workflow is active and configured
3. Create a new company record with a website URL
4. Check that other fields are automatically populated

## Manual Trigger

You can also manually trigger auto-fill:

```typescript
import AutoFillHooks from "~/modules/autoFill/hooks/AutoFillHooks.server";

const result = await AutoFillHooks.manualAutoFill({
  entity,
  tenantId,
  userId,
  rowValues,
  domain: "example.com"
});
```

## Troubleshooting

- Check workflow execution logs in the admin panel
- Verify API credentials are configured
- Ensure field mappings match entity properties
- Check console logs for auto-fill messages
