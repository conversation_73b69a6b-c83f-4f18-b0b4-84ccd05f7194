import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowValueDto } from "~/application/dtos/entities/RowValueDto";
import AutoFillService from "../services/AutoFillService.server";

export interface AutoFillHookParams {
  entity: EntityWithDetails;
  tenantId: string | null;
  userId: string | null;
  rowValues: { dynamicProperties: RowValueDto[] };
  existingRowValues?: { dynamicProperties: RowValueDto[] };
  rowId?: string;
}

/**
 * Hook to be called before row creation to trigger auto-fill
 */
export async function onBeforeRowCreate(params: AutoFillHookParams): Promise<void> {
  const { entity, tenantId, userId, rowValues } = params;
  
  try {
    // Check if auto-fill should be triggered
    const { shouldTrigger, domain, config } = AutoFillService.shouldTriggerAutoFill(
      entity,
      rowValues
    );

    if (!shouldTrigger || !domain || !config) {
      return; // No auto-fill needed
    }

    // Trigger auto-fill workflow
    const result = await AutoFillService.triggerAutoFill(
      entity,
      rowValues,
      domain,
      config,
      { tenantId, userId }
    );

    if (result.success && result.appliedMappings) {
      console.log(`[AutoFill] Successfully enriched ${entity.name} with data for domain: ${domain}`, {
        appliedFields: Object.keys(result.appliedMappings)
      });
    } else if (result.error) {
      console.warn(`[AutoFill] Failed to enrich ${entity.name} for domain: ${domain}`, result.error);
    }

  } catch (error: any) {
    // Don't throw errors - auto-fill should be non-blocking
    console.error(`[AutoFill] Error during auto-fill for ${entity.name}:`, error.message);
  }
}

/**
 * Hook to be called before row update to trigger auto-fill
 */
export async function onBeforeRowUpdate(params: AutoFillHookParams): Promise<void> {
  const { entity, tenantId, userId, rowValues, existingRowValues, rowId } = params;
  
  try {
    // Check if auto-fill should be triggered (only if domain changed)
    const { shouldTrigger, domain, config } = AutoFillService.shouldTriggerAutoFill(
      entity,
      rowValues,
      existingRowValues
    );

    if (!shouldTrigger || !domain || !config) {
      return; // No auto-fill needed
    }

    // Trigger auto-fill workflow
    const result = await AutoFillService.triggerAutoFill(
      entity,
      rowValues,
      domain,
      config,
      { tenantId, userId },
      rowId
    );

    if (result.success && result.appliedMappings) {
      console.log(`[AutoFill] Successfully enriched ${entity.name} update with data for domain: ${domain}`, {
        appliedFields: Object.keys(result.appliedMappings)
      });
    } else if (result.error) {
      console.warn(`[AutoFill] Failed to enrich ${entity.name} update for domain: ${domain}`, result.error);
    }

  } catch (error: any) {
    // Don't throw errors - auto-fill should be non-blocking
    console.error(`[AutoFill] Error during auto-fill update for ${entity.name}:`, error.message);
  }
}

/**
 * Utility function to manually trigger auto-fill for a specific domain
 */
export async function manualAutoFill(params: AutoFillHookParams & { domain: string }): Promise<{
  success: boolean;
  appliedMappings?: Record<string, any>;
  error?: string;
}> {
  const { entity, tenantId, userId, rowValues, domain, rowId } = params;
  
  try {
    const config = AutoFillService['getAutoFillConfig'](entity.name);
    if (!config) {
      return { success: false, error: `No auto-fill configuration found for entity: ${entity.name}` };
    }

    const result = await AutoFillService.triggerAutoFill(
      entity,
      rowValues,
      domain,
      config,
      { tenantId, userId },
      rowId
    );

    return {
      success: result.success,
      appliedMappings: result.appliedMappings,
      error: result.error
    };

  } catch (error: any) {
    return { success: false, error: error.message };
  }
}

export default {
  onBeforeRowCreate,
  onBeforeRowUpdate,
  manualAutoFill
};
