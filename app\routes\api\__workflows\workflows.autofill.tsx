import { ActionFunctionArgs } from "react-router";
import { validate<PERSON>pi<PERSON><PERSON> } from "~/utils/services/apiService";
import WorkflowsExecutionsService from "~/modules/workflowEngine/services/WorkflowsExecutionsService";
import WorkflowsService from "~/modules/workflowEngine/services/WorkflowsService";

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const { tenant, userId } = await validateApiKey(request);
    let body: {
      domain: string;
      entityId?: string;
      entityName?: string;
      workflowId?: string;
    } = {};

    try {
      body = await request.json();
    } catch (e: any) {
      return Response.json({ error: "Invalid JSON body" }, { status: 400 });
    }

    // Validate required fields
    if (!body.domain) {
      return Response.json({ error: "Domain is required" }, { status: 400 });
    }

    // Validate domain format
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(body.domain)) {
      return Response.json({ error: "Invalid domain format" }, { status: 400 });
    }

    let workflowId = body.workflowId;

    // If no workflow ID provided, find the auto-fill workflow
    if (!workflowId) {
      const workflows = await WorkflowsService.getAll({ tenantId: tenant?.id ?? null });
      const autoFillWorkflow = workflows.find(w => 
        w.name === "Auto-Fill Company Data" && w.status === "live"
      );
      
      if (!autoFillWorkflow) {
        return Response.json({ 
          error: "Auto-fill workflow not found. Please create and activate the 'Auto-Fill Company Data' workflow." 
        }, { status: 404 });
      }
      
      workflowId = autoFillWorkflow.id;
    }

    // Execute the workflow
    const execution = await WorkflowsExecutionsService.execute(workflowId, {
      type: "api",
      input: {
        domain: body.domain,
        entityId: body.entityId,
        entityName: body.entityName || "Company"
      },
      session: {
        tenantId: tenant?.id ?? null,
        userId: userId ?? null,
      },
    });

    if (execution.error) {
      return Response.json({ 
        error: execution.error,
        executionId: execution.id 
      }, { status: 400 });
    }

    // Parse the output to extract enriched data
    let enrichedData = null;
    if (execution.output) {
      try {
        const output = JSON.parse(execution.output);
        enrichedData = output.enrichedData || output;
      } catch (e) {
        // If output is not JSON, return as is
        enrichedData = execution.output;
      }
    }

    return Response.json({ 
      success: true,
      execution: {
        id: execution.id,
        status: execution.status,
        duration: execution.duration
      },
      enrichedData,
      domain: body.domain
    }, { status: 200 });

  } catch (e: any) {
    console.error("[Auto-fill API Error]", e);
    return Response.json({ 
      error: e.message || "Internal server error" 
    }, { status: 500 });
  }
};
