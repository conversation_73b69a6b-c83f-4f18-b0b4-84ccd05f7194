import { useState } from "react";
import { useSubmit } from "react-router";
import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import InputText from "~/components/ui/input/InputText";
import InputCheckbox from "~/components/ui/input/InputCheckbox";
import InputSelect from "~/components/ui/input/InputSelect";
import ButtonPrimary from "~/components/ui/buttons/ButtonPrimary";
import ButtonSecondary from "~/components/ui/buttons/ButtonSecondary";
import { PlusIcon, TrashIcon } from "@heroicons/react/24/outline";

export interface AutoFillConfigFormProps {
  entity: EntityWithDetails;
  config?: {
    enabled: boolean;
    triggerField: string;
    workflowName: string;
    apiEndpoint: string;
    fieldMappings: Record<string, string>;
  };
  onSave?: (config: any) => void;
  onCancel?: () => void;
}

export default function AutoFillConfigForm({ 
  entity, 
  config, 
  onSave, 
  onCancel 
}: AutoFillConfigFormProps) {
  const submit = useSubmit();
  
  const [enabled, setEnabled] = useState(config?.enabled ?? false);
  const [triggerField, setTriggerField] = useState(config?.triggerField ?? "website");
  const [workflowName, setWorkflowName] = useState(config?.workflowName ?? "Auto-Fill Company Data");
  const [apiEndpoint, setApiEndpoint] = useState(config?.apiEndpoint ?? "");
  const [fieldMappings, setFieldMappings] = useState<Array<{id: string, apiField: string, entityField: string}>>(
    Object.entries(config?.fieldMappings ?? {}).map(([apiField, entityField], index) => ({
      id: `mapping-${index}`,
      apiField,
      entityField
    }))
  );

  // Get available entity properties for mapping
  const entityProperties = entity.properties.map(p => ({
    value: p.name,
    name: p.title || p.name
  }));

  // Get text-based properties that could trigger auto-fill
  const triggerFieldOptions = entity.properties
    .filter(p => p.type === "text" || p.type === "url")
    .map(p => ({
      value: p.name,
      name: p.title || p.name
    }));

  const addMapping = () => {
    setFieldMappings([
      ...fieldMappings,
      {
        id: `mapping-${Date.now()}`,
        apiField: "",
        entityField: ""
      }
    ]);
  };

  const removeMapping = (id: string) => {
    setFieldMappings(fieldMappings.filter(m => m.id !== id));
  };

  const updateMapping = (id: string, field: "apiField" | "entityField", value: string) => {
    setFieldMappings(fieldMappings.map(m => 
      m.id === id ? { ...m, [field]: value } : m
    ));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const mappingsObject = fieldMappings.reduce((acc, mapping) => {
      if (mapping.apiField && mapping.entityField) {
        acc[mapping.apiField] = mapping.entityField;
      }
      return acc;
    }, {} as Record<string, string>);

    const formData = new FormData();
    formData.set("action", "save-autofill-config");
    formData.set("entityId", entity.id);
    formData.set("enabled", enabled.toString());
    formData.set("triggerField", triggerField);
    formData.set("workflowName", workflowName);
    formData.set("apiEndpoint", apiEndpoint);
    formData.set("fieldMappings", JSON.stringify(mappingsObject));

    if (onSave) {
      onSave({
        enabled,
        triggerField,
        workflowName,
        apiEndpoint,
        fieldMappings: mappingsObject
      });
    } else {
      submit(formData, { method: "post" });
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Auto-Fill Configuration for {entity.title}
        </h3>

        <div className="space-y-4">
          <InputCheckbox
            name="enabled"
            title="Enable Auto-Fill"
            description="Automatically populate fields when domain/website is entered"
            value={enabled}
            setValue={setEnabled}
          />

          {enabled && (
            <>
              <InputSelect
                name="triggerField"
                title="Trigger Field"
                description="Field that triggers auto-fill when filled"
                value={triggerField}
                setValue={setTriggerField}
                options={triggerFieldOptions}
                required
              />

              <InputText
                name="workflowName"
                title="Workflow Name"
                description="Name of the workflow to execute for auto-fill"
                value={workflowName}
                setValue={setWorkflowName}
                required
              />

              <InputText
                name="apiEndpoint"
                title="API Endpoint"
                description="Base URL for the data enrichment API"
                value={apiEndpoint}
                setValue={setApiEndpoint}
                placeholder="https://api.example.com/v1"
                required
              />
            </>
          )}
        </div>
      </div>

      {enabled && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-medium text-gray-900">Field Mappings</h4>
            <ButtonSecondary onClick={addMapping} type="button">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Mapping
            </ButtonSecondary>
          </div>

          <div className="space-y-3">
            {fieldMappings.map((mapping) => (
              <div key={mapping.id} className="flex items-center space-x-3">
                <div className="flex-1">
                  <InputText
                    name={`apiField-${mapping.id}`}
                    title=""
                    placeholder="API response field (e.g., basicInformation.company_name)"
                    value={mapping.apiField}
                    setValue={(value) => updateMapping(mapping.id, "apiField", value)}
                  />
                </div>
                <div className="text-gray-500">→</div>
                <div className="flex-1">
                  <InputSelect
                    name={`entityField-${mapping.id}`}
                    title=""
                    placeholder="Select entity property"
                    value={mapping.entityField}
                    setValue={(value) => updateMapping(mapping.id, "entityField", value)}
                    options={entityProperties}
                  />
                </div>
                <button
                  type="button"
                  onClick={() => removeMapping(mapping.id)}
                  className="text-red-600 hover:text-red-800 p-1"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            ))}

            {fieldMappings.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                No field mappings configured. Click "Add Mapping" to start.
              </div>
            )}
          </div>
        </div>
      )}

      <div className="flex justify-end space-x-3">
        {onCancel && (
          <ButtonSecondary onClick={onCancel} type="button">
            Cancel
          </ButtonSecondary>
        )}
        <ButtonPrimary type="submit">
          Save Configuration
        </ButtonPrimary>
      </div>
    </form>
  );
}
