import { Ref, forwardRef, useImperativeHandle, useRef, ReactNode, Fragment, useEffect, useState } from "react";
import { Combobox, ComboboxButton, ComboboxOption, ComboboxOptions, Label, Transition } from "@headlessui/react";
import { useTranslation } from "react-i18next";
import { Link } from "react-router";
import { Colors } from "~/application/enums/shared/Colors";
import ColorBadge from "../badges/ColorBadge";
import HintTooltip from "../tooltips/HintTooltip";
import EntityIcon from "~/components/layouts/icons/EntityIcon";
import { Input } from "../input";
import { CaretSortIcon, CheckIcon } from "@radix-ui/react-icons";
import { cn } from "~/lib/utils";

export interface RefInputCombobox {
  focus: () => void;
}

interface Props {
  name?: string;
  title?: string;
  value?: (string | number)[];
  disabled?: boolean;
  options: { value: string | number | undefined; name?: string | ReactNode; color?: Colors; disabled?: boolean }[];
  onChange?: (value: (string | number)[]) => void;
  className?: string;
  withSearch?: boolean;
  withLabel?: boolean;
  withColors?: boolean;
  selectPlaceholder?: string;
  onNew?: () => void;
  onNewRoute?: string;
  required?: boolean;
  help?: string;
  hint?: ReactNode;
  icon?: string;
  borderless?: boolean;
  darkMode?: boolean;
  autoFocus?: boolean;
  readOnly?: boolean;
  renderHiddenInputValue?: (item: string | number) => string;
  prefix?: string;
  onClear?: () => void;
  minDisplayCount?: number;
}
const InputCombobox = (
  {
    name,
    title,
    value,
    options,
    disabled,
    onChange,
    className,
    withSearch = true,
    withLabel = true,
    withColors = false,
    selectPlaceholder,
    onNew,
    required,
    onNewRoute,
    help,
    hint,
    icon,
    borderless,
    darkMode,
    autoFocus,
    readOnly,
    renderHiddenInputValue,
    prefix,
    onClear,
    minDisplayCount = 3,
  }: Props,
  ref: Ref<RefInputCombobox>
) => {
  const { t } = useTranslation();

  const button = useRef<HTMLButtonElement>(null);
  const inputSearch = useRef<HTMLInputElement>(null);

  const [selected, setSelected] = useState<(string | number)[]>(value || []);
  const [searchInput, setSearchInput] = useState("");

  useEffect(() => {
    if (value && !isEqual(selected.sort(), value?.sort())) {
      setSelected(value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  useEffect(() => {
    if (onChange && !isEqual(selected.sort(), value?.sort())) {
      onChange(selected);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selected]);

  function isEqual(a: any, b: any) {
    return JSON.stringify(a) === JSON.stringify(b);
  }

  useImperativeHandle(ref, () => ({ focus }));
  function focus() {
    setTimeout(() => {
      button.current?.focus();
      button.current?.click();
    }, 1);
  }

  const filteredItems = () => {
    if (!options) {
      return [];
    }
    return options.filter(
      (f) => f.name?.toString().toUpperCase().includes(searchInput.toUpperCase()) || f.value?.toString().toUpperCase().includes(searchInput.toUpperCase())
    );
  };

  function getSelectedOptions() {
    return options.filter((f) => selected.includes(f.value as string | number));
  }

  return (
    // @ts-ignore
    <Combobox multiple value={selected} onChange={setSelected} disabled={disabled || readOnly}>
      {({ open }) => (
        <div>
          {/* {renderHiddenInputValue && <>
            {selected?.map((item, idx) => {
            return <input key={idx} type="hidden" name={name + `[]`} value={JSON.stringify(item)} />;
          })}
          </>} */}

          {withLabel && title && (
            <Label htmlFor={name} className="mb-1 flex justify-between space-x-2 text-xs font-medium">
              <div className="flex items-center space-x-1">
                <div className="truncate">
                  {title}
                  {required && <span className="ml-1 text-red-500">*</span>}
                </div>

                {help && <HintTooltip text={help} />}
              </div>
              {hint}
              {onClear && selected.length > 0 ? (
                <button type="button" onClick={onClear} className="text-muted-foreground hover:text-foreground/80 text-xs focus:outline-hidden">
                  {t("shared.clear")}
                </button>
              ) : null}
            </Label>
          )}

          <div className="relative">
            <ComboboxButton
              autoFocus={autoFocus}
              type="button"
              ref={button}
              // className={cn(
              //   "focus:border-border focus:ring-ring relative w-full cursor-default rounded-md border border-border py-2 pl-3 pr-10 text-left shadow-2xs focus:outline-hidden focus:ring-1 sm:text-sm",
              //   disabled || readOnly ? "cursor-not-allowed bg-secondary/90" : "bg-background hover:bg-secondary focus:bg-secondary",
              //   borderless && "border-transparent",
              //   darkMode && "dark:border-border dark:bg-foreground/90"
              // )}
              className={cn(
                className,
                "border-input ring-offset-background placeholder:text-muted-foreground focus:ring-ring flex h-9 w-full items-center justify-between rounded-md border bg-transparent px-3 py-2 text-left text-sm whitespace-nowrap shadow-2xs focus:ring-1 focus:outline-hidden disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1"
              )}
            >
              {icon && (
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <EntityIcon className="h-5 w-5" icon={icon} />
                </div>
              )}

              <div className="inline-flex w-full items-center space-x-2 truncate">
                {/* {withColors && selected && <ColorBadge color={selected?.color ?? Colors.UNDEFINED} />} */}
                <div className="truncate">
                  {/* {JSON.stringify(selected)} */}
                  {selected.length > 0 ? (
                    <span className="truncate">
                      {prefix ?? ""}
                      {getSelectedOptions().length < minDisplayCount
                        ? getSelectedOptions()
                            .map((f) => f.name ?? f.value)
                            .join(", ")
                        : getSelectedOptions().length + " selected"}
                    </span>
                  ) : (
                    <span className="text-muted-foreground text-sm">{selectPlaceholder ?? t("shared.select")}...</span>
                  )}
                </div>
              </div>
              <CaretSortIcon className="h-4 w-4 opacity-50" />
            </ComboboxButton>

            <Transition show={open} as={Fragment} leave="transition ease-in duration-100" leaveFrom="opacity-100" leaveTo="opacity-0">
              <ComboboxOptions
                // onFocus={() => inputSearch.current?.focus()}
                // onBlur={() => setSearchInput("")}
                className={cn(
                  "bg-background text-foreground border-border absolute z-10 mt-1 max-h-72 w-full overflow-auto rounded-md border py-1 text-base shadow-lg focus:outline-hidden sm:text-sm"
                )}
              >
                {((withSearch && filteredItems().length > 10) || onNew || onNewRoute) && (
                  <div className="flex rounded-md p-2">
                    {withSearch && filteredItems().length > 10 && (
                      <div className="relative flex grow items-stretch focus-within:z-10">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="text-muted-foreground h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                        </div>
                        <Input
                          ref={inputSearch}
                          id="search"
                          autoComplete="off"
                          placeholder={t("shared.searchDot")}
                          value={searchInput}
                          onChange={(e) => setSearchInput(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    )}
                    {onNew && (
                      <button
                        title={t("shared.new")}
                        type="button"
                        onClick={onNew}
                        className="focus:border-border focus:ring-ring relative -ml-px inline-flex items-center space-x-2 rounded-r-md px-2 py-2 text-sm font-medium focus:ring-1 focus:outline-hidden"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </button>
                    )}

                    {onNewRoute && (
                      <Link
                        to={onNewRoute}
                        className="focus:border-border focus:ring-ring border-border bg-secondary hover:bg-secondary/90 text-foreground/80 relative -ml-px inline-flex items-center space-x-2 rounded-r-md border px-2 py-2 text-sm font-medium focus:ring-1 focus:outline-hidden"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </Link>
                    )}
                  </div>
                )}

                {filteredItems().map((item, idx) => (
                  <ComboboxOption
                    key={idx}
                    disabled={item.disabled}
                    className={({ focus, selected }) =>
                      cn(
                        "text-foreground hover:bg-secondary hover:text-secondary-foreground relative cursor-default py-2 pr-9 pl-3 select-none focus:outline-hidden",
                        !item.disabled && focus && "bg-background text-foreground",
                        !item.disabled && !focus && " ",
                        item.disabled && "text-muted-foreground bg-secondary/90 cursor-not-allowed",
                        darkMode && !item.disabled && focus && "dark:bg-accent-500 dark:text-black",
                        darkMode && !item.disabled && !focus && "dark:text-gray-50",
                        darkMode && item.disabled && "dark:text-muted-foreground cursor-not-allowed dark:bg-gray-900"
                        // selected && "bg-secondary text-secondary-foreground"
                      )
                    }
                    value={item.value}
                  >
                    {({ selected, active }) => (
                      <div className="flex items-center justify-between space-x-2">
                        <div className="flex items-center space-x-2 truncate">
                          {withColors && item.color !== undefined && <ColorBadge color={item.color} />}
                          <div className={cn(selected ? "font-normal" : "font-normal", "truncate")}>{item.name || item.value}</div>
                        </div>

                        {selected ? (
                          <div className={cn(active ? "" : "", "absolute inset-y-0 right-0 flex items-center pr-4")}>
                            <CheckIcon className="h-4 w-4" />
                            {/* <svg xmlns="http://www.w3.org/2000/svg" className="text-foreground h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg> */}
                          </div>
                        ) : null}
                      </div>
                    )}
                  </ComboboxOption>
                ))}

                {withSearch && filteredItems().length === 0 && <div className="text-muted-foreground px-3 py-2 text-sm">{t("shared.noRecords")}</div>}
              </ComboboxOptions>
            </Transition>
          </div>
        </div>
      )}
    </Combobox>
  );
};

export default forwardRef(InputCombobox);
