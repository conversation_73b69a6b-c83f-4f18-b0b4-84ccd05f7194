import { useEffect, useState } from "react";
import { CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from "@heroicons/react/24/outline";
import clsx from "clsx";

export interface AutoFillNotificationProps {
  show: boolean;
  type: "loading" | "success" | "error" | "info";
  message: string;
  details?: string;
  onClose?: () => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export default function AutoFillNotification({
  show,
  type,
  message,
  details,
  onClose,
  autoClose = true,
  autoCloseDelay = 5000
}: AutoFillNotificationProps) {
  const [isVisible, setIsVisible] = useState(show);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  useEffect(() => {
    if (show && autoClose && (type === "success" || type === "info")) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onClose?.();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [show, autoClose, autoCloseDelay, type, onClose]);

  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case "loading":
        return (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        );
      case "success":
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case "error":
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />;
      case "info":
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-600" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case "loading":
        return "bg-blue-50 border-blue-200";
      case "success":
        return "bg-green-50 border-green-200";
      case "error":
        return "bg-red-50 border-red-200";
      case "info":
      default:
        return "bg-blue-50 border-blue-200";
    }
  };

  const getTextColor = () => {
    switch (type) {
      case "loading":
        return "text-blue-800";
      case "success":
        return "text-green-800";
      case "error":
        return "text-red-800";
      case "info":
      default:
        return "text-blue-800";
    }
  };

  return (
    <div
      className={clsx(
        "fixed top-4 right-4 z-50 max-w-sm w-full shadow-lg rounded-lg border p-4 transition-all duration-300",
        getBackgroundColor(),
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-2"
      )}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          <p className={clsx("text-sm font-medium", getTextColor())}>
            {message}
          </p>
          {details && (
            <p className={clsx("mt-1 text-xs", getTextColor(), "opacity-75")}>
              {details}
            </p>
          )}
        </div>
        {onClose && type !== "loading" && (
          <div className="ml-4 flex-shrink-0">
            <button
              type="button"
              className={clsx(
                "inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2",
                type === "success" && "text-green-500 hover:bg-green-100 focus:ring-green-600",
                type === "error" && "text-red-500 hover:bg-red-100 focus:ring-red-600",
                (type === "info" || type === "loading") && "text-blue-500 hover:bg-blue-100 focus:ring-blue-600"
              )}
              onClick={() => {
                setIsVisible(false);
                onClose();
              }}
            >
              <span className="sr-only">Dismiss</span>
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// Hook for managing auto-fill notifications
export function useAutoFillNotification() {
  const [notification, setNotification] = useState<{
    show: boolean;
    type: AutoFillNotificationProps["type"];
    message: string;
    details?: string;
  }>({
    show: false,
    type: "info",
    message: ""
  });

  const showNotification = (
    type: AutoFillNotificationProps["type"],
    message: string,
    details?: string
  ) => {
    setNotification({
      show: true,
      type,
      message,
      details
    });
  };

  const hideNotification = () => {
    setNotification(prev => ({ ...prev, show: false }));
  };

  const showLoading = (message: string = "Enriching company data...") => {
    showNotification("loading", message);
  };

  const showSuccess = (message: string, details?: string) => {
    showNotification("success", message, details);
  };

  const showError = (message: string, details?: string) => {
    showNotification("error", message, details);
  };

  const showInfo = (message: string, details?: string) => {
    showNotification("info", message, details);
  };

  return {
    notification,
    showNotification,
    hideNotification,
    showLoading,
    showSuccess,
    showError,
    showInfo,
    NotificationComponent: () => (
      <AutoFillNotification
        show={notification.show}
        type={notification.type}
        message={notification.message}
        details={notification.details}
        onClose={hideNotification}
      />
    )
  };
}
