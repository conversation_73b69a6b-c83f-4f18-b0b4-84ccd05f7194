{
  "include": [
    "env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".react-router/types/**/*", //
    "app/**/*"
  ],
  "compilerOptions": {
    "types": ["@react-router/node", "vite/client"],
    "skipLibCheck": true,
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "strict": true,
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"],
      "~/public/*": ["./public/*"]
    },
    "noEmit": true,
    "forceConsistentCasingInFileNames": true,
    "allowJs": true,
    "allowImportingTsExtensions": true,
    "rootDirs": [".", "./.react-router/types"]
  }
}
