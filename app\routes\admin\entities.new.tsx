import { useTranslation } from "react-i18next";
import { ActionFunction, LoaderFunctionArgs, redirect } from "react-router";
import EntityForm from "~/components/entities/EntityForm";
import NewPageLayout from "~/components/ui/layouts/NewPageLayout";
import { getTranslations } from "~/locale/i18next.server";
import { createEntity } from "~/utils/db/entities/entities.db.server";
import { createEntityPermissions } from "~/utils/db/permissions/permissions.db.server";
import { EntitiesApi } from "~/utils/api/.server/EntitiesApi";
import { verifyUserHasPermission } from "~/utils/helpers/.server/PermissionsService";
import Constants from "~/application/Constants";
import { getAllTenantTypes } from "~/utils/db/tenants/tenantTypes.db.server";
import { cache } from "~/utils/cache.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  await verifyUserHasPermission(request, "admin.entities.create");
  return Response.json({});
};

type ActionData = {
  error?: string;
};
const badRequest = (data: ActionData) => Response.json(data, { status: 400 });
export const action: ActionFunction = async ({ request }) => {
  await verifyUserHasPermission(request, "admin.entities.create");
  const { t } = await getTranslations(request);
  const form = await request.formData();
  const action = form.get("action")?.toString() ?? "";
  if (action === "create") {
    const name = form.get("name")?.toString() ?? "";
    const slug = form.get("slug")?.toString().toLowerCase() ?? "";
    // const order = Number(form.get("order"));
    const prefix = form.get("prefix")?.toString() ?? "";
    const title = form.get("title")?.toString() ?? "";
    const titlePlural = form.get("title-plural")?.toString() ?? "";
    const isAutogenerated = Boolean(form.get("is-autogenerated"));
    const hasApi = Boolean(form.get("has-api"));
    const icon = form.get("icon")?.toString() ?? "";
    const type = form.get("type")?.toString() ?? "";
    const active = Boolean(form.get("active"));

    const showInSidebar = Boolean(form.get("show-in-sidebar"));
    const hasTags = Boolean(form.get("has-tags"));
    const hasComments = Boolean(form.get("has-comments"));
    const hasTasks = Boolean(form.get("has-tasks"));
    const hasActivity = Boolean(form.get("has-activity"));
    const hasBulkDelete = Boolean(form.get("has-bulk-delete"));
    const hasViews = Boolean(form.get("has-views"));

    const defaultVisibility = form.get("default-visibility")?.toString() ?? Constants.DEFAULT_ROW_VISIBILITY;

    const onCreated = form.get("onCreated")?.toString() ?? "redirectToOverview";
    const onEdit = form.get("onEdit")?.toString() ?? "editRoute";

    const errors = await EntitiesApi.validateEntity({ tenantId: null, name, slug, order: null, prefix });
    if (errors.length > 0) {
      return badRequest({ error: errors.join(", ") });
    }
    try {
      const entity = await createEntity({
        name,
        slug,
        prefix,
        title,
        titlePlural,
        isAutogenerated,
        hasApi,
        icon,
        active,
        type,
        showInSidebar,
        hasTags,
        hasComments,
        hasTasks,
        hasActivity,
        hasBulkDelete,
        hasViews,
        defaultVisibility,
        onCreated,
        onEdit,
      });

      await createEntityPermissions(entity);

      cache.clear();
      if (entity) {
        return redirect(`/admin/entities/${slug}/properties`);
      } else {
        return badRequest({ error: "Could not create entity" });
      }
    } catch (e: any) {
      return badRequest({ error: e.message });
    }
  } else {
    return badRequest({ error: t("shared.invalidForm") });
  }
};

export default function NewEntityRoute() {
  const { t } = useTranslation();
  return (
    <NewPageLayout
      title={`${t("shared.create")} ${t("models.entity.object")}`}
      menu={[
        { title: t("models.entity.plural"), routePath: "/admin/entities" },
        { title: t("shared.new"), routePath: "/admin/entities/new" },
      ]}
    >
      <EntityForm />
    </NewPageLayout>
  );
}
