model AutoFillConfig {
  id              String   @id @default(cuid())
  entityName      String   @unique
  enabled         Boolean  @default(true)
  triggerField    String
  workflowName    String?
  minDomainLength Int?     @default(3)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  fieldMappings AutoFillFieldMapping[]

  @@map("AutoFillConfig")
}

model AutoFillFieldMapping {
  id          String   @id @default(cuid())
  configId    String
  apiField    String
  entityField String
  createdAt   DateTime @default(now())

  config AutoFillConfig @relation(fields: [configId], references: [id], onDelete: Cascade)

  @@index([configId])
  @@map("AutoFillFieldMapping")
}
