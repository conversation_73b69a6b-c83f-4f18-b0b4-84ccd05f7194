import { db } from "~/utils/db.server";
import WorkflowsService from "~/modules/workflowEngine/services/WorkflowsService";
import { WorkflowStatus } from "@prisma/client";

export interface AutoFillWorkflowSetup {
  tenantId: string | null;
  apiEndpoint: string;
  apiKey: string;
  workflowName?: string;
}

/**
 * Sets up the auto-fill workflow with proper variables and credentials
 */
export async function setupAutoFillWorkflow({
  tenantId,
  apiEndpoint,
  apiKey,
  workflowName = "Auto-Fill Company Data"
}: AutoFillWorkflowSetup) {
  
  try {
    // Find existing workflow or create from template
    const workflows = await WorkflowsService.getAll({ tenantId });
    let workflow = workflows.find(w => w.name === workflowName);
    
    if (!workflow) {
      // Create workflow from template
      const template = getAutoFillWorkflowTemplate();
      workflow = await WorkflowsService.create({
        tenantId,
        name: workflowName,
        description: "Automatically enriches company data based on domain input",
        status: WorkflowStatus.draft,
        inputExamples: JSON.stringify(template.inputExamples),
        blocks: template.blocks,
        toBlocks: template.toBlocks,
      });
    }

    // Setup workflow variables
    await setupWorkflowVariables(workflow.id, {
      enrichmentApiUrl: apiEndpoint,
      fieldMappings: getDefaultFieldMappings()
    });

    // Setup workflow credentials
    await setupWorkflowCredentials(workflow.id, {
      enrichmentApiKey: apiKey
    });

    // Activate the workflow
    await WorkflowsService.update(workflow.id, {
      status: WorkflowStatus.live
    });

    return {
      success: true,
      workflowId: workflow.id,
      message: `Auto-fill workflow "${workflowName}" has been set up successfully`
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Failed to setup auto-fill workflow"
    };
  }
}

/**
 * Sets up workflow variables
 */
async function setupWorkflowVariables(workflowId: string, variables: Record<string, any>) {
  for (const [name, value] of Object.entries(variables)) {
    // Check if variable already exists
    const existing = await db.workflowVariable.findFirst({
      where: { workflowId, name }
    });

    const variableValue = typeof value === 'object' ? JSON.stringify(value) : String(value);

    if (existing) {
      await db.workflowVariable.update({
        where: { id: existing.id },
        data: { value: variableValue }
      });
    } else {
      await db.workflowVariable.create({
        data: {
          workflowId,
          name,
          value: variableValue
        }
      });
    }
  }
}

/**
 * Sets up workflow credentials
 */
async function setupWorkflowCredentials(workflowId: string, credentials: Record<string, string>) {
  for (const [name, value] of Object.entries(credentials)) {
    // Check if credential already exists
    const existing = await db.workflowCredential.findFirst({
      where: { workflowId, name }
    });

    if (existing) {
      await db.workflowCredential.update({
        where: { id: existing.id },
        data: { value }
      });
    } else {
      await db.workflowCredential.create({
        data: {
          workflowId,
          name,
          value
        }
      });
    }
  }
}

/**
 * Gets the default field mappings for company data
 */
function getDefaultFieldMappings() {
  return {
    "basicInformation.company_name": "name",
    "basicInformation.website_url": "website", 
    "basicInformation.industry": "industry",
    "basicInformation.sub_industry": "subIndustry",
    "basicInformation.about": "description",
    "basicInformation.logo": "logo",
    "locationInformation.address": "address",
    "locationInformation.city": "city",
    "locationInformation.state": "state",
    "locationInformation.country": "country",
    "locationInformation.primary_phone": "phone",
    "companyInfo.size_of_company": "employees",
    "companyInfo.founding_year": "founded",
    "companyInfo.revenue_range": "revenue",
    "companyInfo.is_parent_company": "isParentCompany",
    "companyInfo.parent_company": "parentCompany",
    "socialLinks.linkedin_url": "linkedin",
    "socialLinks.twitter_url": "twitter",
    "socialLinks.facebook_url": "facebook",
    "socialLinks.instagram_url": "instagram",
    "socialLinks.crunchbase_url": "crunchbase",
    "technographics": "technologies"
  };
}

/**
 * Gets the workflow template structure
 */
function getAutoFillWorkflowTemplate() {
  return {
    inputExamples: [
      {
        title: "Enrich Google data",
        input: {
          domain: "google.com",
          entityId: "clx123456789",
          entityName: "Company"
        }
      }
    ],
    blocks: [
      {
        id: "manualTrigger",
        type: "manual",
        description: "Triggers the workflow with domain parameter",
        input: {
          validation: JSON.stringify({
            type: "object",
            properties: {
              domain: { type: "string", pattern: "^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\\.[a-zA-Z]{2,}$" },
              entityId: { type: "string" },
              entityName: { type: "string" }
            },
            required: ["domain"],
          }, null, 2),
        },
        conditionGroups: [],
      },
      {
        id: "enrichmentRequest",
        type: "httpRequest",
        description: "Fetches company data from enrichment API",
        input: {
          url: "{{$vars.enrichmentApiUrl}}/company?domain={{$params.domain}}",
          method: "GET",
          headers: {
            "Authorization": "Bearer {{$credentials.enrichmentApiKey}}",
            "Content-Type": "application/json"
          },
          throwsError: false,
        },
        conditionGroups: [],
      }
      // Additional blocks would be added here...
    ],
    toBlocks: [
      {
        fromBlockId: "manualTrigger",
        toBlockId: "enrichmentRequest",
      }
      // Additional connections would be added here...
    ]
  };
}

/**
 * Validates the auto-fill setup
 */
export async function validateAutoFillSetup(tenantId: string | null, workflowName: string = "Auto-Fill Company Data") {
  try {
    const workflows = await WorkflowsService.getAll({ tenantId });
    const workflow = workflows.find(w => w.name === workflowName);
    
    if (!workflow) {
      return { valid: false, error: "Auto-fill workflow not found" };
    }

    if (workflow.status !== WorkflowStatus.live) {
      return { valid: false, error: "Auto-fill workflow is not active" };
    }

    // Check for required variables
    const variables = await db.workflowVariable.findMany({
      where: { workflowId: workflow.id }
    });

    const hasApiUrl = variables.some(v => v.name === "enrichmentApiUrl" && v.value);
    if (!hasApiUrl) {
      return { valid: false, error: "API endpoint not configured" };
    }

    // Check for required credentials
    const credentials = await db.workflowCredential.findMany({
      where: { workflowId: workflow.id }
    });

    const hasApiKey = credentials.some(c => c.name === "enrichmentApiKey" && c.value);
    if (!hasApiKey) {
      return { valid: false, error: "API key not configured" };
    }

    return { 
      valid: true, 
      workflowId: workflow.id,
      message: "Auto-fill setup is valid and ready to use"
    };

  } catch (error: any) {
    return { valid: false, error: error.message };
  }
}

export default {
  setupAutoFillWorkflow,
  validateAutoFillSetup
};
