import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowValueDto } from "~/application/dtos/entities/RowValueDto";
import WorkflowsExecutionsService from "~/modules/workflowEngine/services/WorkflowsExecutionsService";
import WorkflowsService from "~/modules/workflowEngine/services/WorkflowsService";

export interface AutoFillConfig {
  enabled: boolean;
  entityName: string;
  triggerField: string; // e.g., "website", "domain"
  workflowName?: string;
  fieldMappings: Record<string, string>; // API response field -> entity property mapping
  minDomainLength?: number;
}

export interface AutoFillResult {
  success: boolean;
  enrichedData?: Record<string, any>;
  error?: string;
  appliedMappings?: Record<string, any>;
}

// Default configurations for different entities
const DEFAULT_CONFIGS: Record<string, AutoFillConfig> = {
  Company: {
    enabled: true,
    entityName: "Company",
    triggerField: "website",
    workflowName: "Auto-Fill Company Data",
    minDomainLength: 3,
    fieldMappings: {
      "name": "name",
      "industry": "industry", 
      "description": "description",
      "employees": "employees",
      "revenue": "revenue",
      "founded": "founded",
      "headquarters": "headquarters",
      "phone": "phone",
      "linkedin": "linkedin",
      "twitter": "twitter"
    }
  },
  Organization: {
    enabled: true,
    entityName: "Organization", 
    triggerField: "website",
    workflowName: "Auto-Fill Company Data",
    minDomainLength: 3,
    fieldMappings: {
      "name": "organizationName",
      "industry": "industry",
      "description": "description"
    }
  }
};

class AutoFillService {
  
  /**
   * Checks if auto-fill should be triggered for the given entity and row values
   */
  static shouldTriggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    existingRowValues?: { dynamicProperties: RowValueDto[] }
  ): { shouldTrigger: boolean; domain?: string; config?: AutoFillConfig } {
    
    const config = this.getAutoFillConfig(entity.name);
    if (!config || !config.enabled) {
      return { shouldTrigger: false };
    }

    // Find the trigger field (e.g., website)
    const triggerProperty = entity.properties.find(p => p.name === config.triggerField);
    if (!triggerProperty) {
      return { shouldTrigger: false };
    }

    // Get the current value of the trigger field
    const currentValue = rowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
    if (!currentValue?.textValue) {
      return { shouldTrigger: false };
    }

    // Extract domain from URL or use as-is if it's already a domain
    const domain = this.extractDomain(currentValue.textValue);
    if (!domain || domain.length < (config.minDomainLength || 3)) {
      return { shouldTrigger: false };
    }

    // Check if this is a new value (for updates, only trigger if domain changed)
    if (existingRowValues) {
      const existingValue = existingRowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
      const existingDomain = existingValue?.textValue ? this.extractDomain(existingValue.textValue) : null;
      
      if (existingDomain === domain) {
        return { shouldTrigger: false }; // Domain hasn't changed
      }
    }

    return { shouldTrigger: true, domain, config };
  }

  /**
   * Triggers auto-fill workflow and applies results to row values
   */
  static async triggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    domain: string,
    config: AutoFillConfig,
    session: { tenantId: string | null; userId: string | null },
    rowId?: string
  ): Promise<AutoFillResult> {
    
    try {
      // Find the auto-fill workflow
      const workflows = await WorkflowsService.getAll({ tenantId: session.tenantId });
      const autoFillWorkflow = workflows.find(w => 
        w.name === (config.workflowName || "Auto-Fill Company Data") && w.status === "live"
      );
      
      if (!autoFillWorkflow) {
        return {
          success: false,
          error: `Auto-fill workflow '${config.workflowName}' not found or not active`
        };
      }

      // Execute the workflow
      const execution = await WorkflowsExecutionsService.execute(autoFillWorkflow.id, {
        type: "manual",
        input: {
          domain,
          entityId: rowId,
          entityName: entity.name
        },
        session
      });

      if (execution.error) {
        return {
          success: false,
          error: execution.error
        };
      }

      // Parse the enriched data from workflow output
      let enrichedData: Record<string, any> = {};
      if (execution.output) {
        try {
          const output = JSON.parse(execution.output);
          enrichedData = output.enrichedData || output;
        } catch (e) {
          // If output is not JSON, skip auto-fill
          return {
            success: false,
            error: "Invalid workflow output format"
          };
        }
      }

      // Apply field mappings to row values
      const appliedMappings = this.applyFieldMappings(
        entity,
        rowValues,
        enrichedData,
        config.fieldMappings
      );

      return {
        success: true,
        enrichedData,
        appliedMappings
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || "Auto-fill failed"
      };
    }
  }

  /**
   * Applies field mappings from enriched data to row values
   */
  private static applyFieldMappings(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    enrichedData: Record<string, any>,
    fieldMappings: Record<string, string>
  ): Record<string, any> {
    
    const appliedMappings: Record<string, any> = {};

    for (const [apiField, entityField] of Object.entries(fieldMappings)) {
      const enrichedValue = enrichedData[apiField];
      if (!enrichedValue) continue;

      // Find the entity property
      const property = entity.properties.find(p => p.name === entityField);
      if (!property) continue;

      // Check if the field is already populated (don't overwrite existing data)
      const existingValue = rowValues.dynamicProperties.find(p => p.propertyId === property.id);
      if (existingValue?.textValue) continue; // Skip if already has value

      // Add or update the row value
      const existingIndex = rowValues.dynamicProperties.findIndex(p => p.propertyId === property.id);
      const newValue: RowValueDto = {
        propertyId: property.id,
        textValue: String(enrichedValue),
        numberValue: null,
        dateValue: null,
        booleanValue: null,
        media: []
      };

      if (existingIndex >= 0) {
        rowValues.dynamicProperties[existingIndex] = newValue;
      } else {
        rowValues.dynamicProperties.push(newValue);
      }

      appliedMappings[entityField] = enrichedValue;
    }

    return appliedMappings;
  }

  /**
   * Extracts domain from URL or validates domain format
   */
  private static extractDomain(input: string): string | null {
    if (!input) return null;

    // Remove protocol and www
    let domain = input.toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .split('/')[0]
      .split('?')[0]
      .split('#')[0];

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain) ? domain : null;
  }

  /**
   * Gets auto-fill configuration for an entity
   */
  private static getAutoFillConfig(entityName: string): AutoFillConfig | null {
    return DEFAULT_CONFIGS[entityName] || null;
  }

  /**
   * Updates auto-fill configuration for an entity
   */
  static setAutoFillConfig(entityName: string, config: AutoFillConfig): void {
    DEFAULT_CONFIGS[entityName] = config;
  }

  /**
   * Gets all auto-fill configurations
   */
  static getAllConfigs(): Record<string, AutoFillConfig> {
    return { ...DEFAULT_CONFIGS };
  }
}

export default AutoFillService;
