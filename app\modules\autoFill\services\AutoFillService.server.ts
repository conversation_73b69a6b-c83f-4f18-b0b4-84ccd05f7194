import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowValueDto } from "~/application/dtos/entities/RowValueDto";
import WorkflowsExecutionsService from "~/modules/workflowEngine/services/WorkflowsExecutionsService";
import WorkflowsService from "~/modules/workflowEngine/services/WorkflowsService";

export interface AutoFillConfig {
  enabled: boolean;
  entityName: string;
  triggerField: string; // e.g., "website", "domain"
  workflowName?: string;
  fieldMappings: Record<string, string>; // API response field -> entity property mapping
  minDomainLength?: number;
}

export interface AutoFillResult {
  success: boolean;
  enrichedData?: Record<string, any>;
  error?: string;
  appliedMappings?: Record<string, any>;
}

// Dynamic configurations stored in database - this will be populated from DB
let DYNAMIC_CONFIGS: Record<string, AutoFillConfig> = {};

// Default fallback configuration for any entity
const DEFAULT_CONFIG_TEMPLATE: Omit<AutoFillConfig, 'entityName' | 'fieldMappings'> = {
  enabled: true,
  triggerField: "website",
  workflowName: "Auto-Fill Company Data",
  minDomainLength: 3
};

class AutoFillService {
  
  /**
   * Checks if auto-fill should be triggered for the given entity and row values
   */
  static async shouldTriggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    existingRowValues?: { dynamicProperties: RowValueDto[] }
  ): Promise<{ shouldTrigger: boolean; domain?: string; config?: AutoFillConfig }> {

    const config = await this.getAutoFillConfig(entity.name);
    if (!config || !config.enabled) {
      return { shouldTrigger: false };
    }

    // Find the trigger field (e.g., website)
    const triggerProperty = entity.properties.find(p => p.name === config.triggerField);
    if (!triggerProperty) {
      return { shouldTrigger: false };
    }

    // Get the current value of the trigger field
    const currentValue = rowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
    if (!currentValue?.textValue) {
      return { shouldTrigger: false };
    }

    // Extract domain from URL or use as-is if it's already a domain
    const domain = this.extractDomain(currentValue.textValue);
    if (!domain || domain.length < (config.minDomainLength || 3)) {
      return { shouldTrigger: false };
    }

    // Check if this is a new value (for updates, only trigger if domain changed)
    if (existingRowValues) {
      const existingValue = existingRowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
      const existingDomain = existingValue?.textValue ? this.extractDomain(existingValue.textValue) : null;
      
      if (existingDomain === domain) {
        return { shouldTrigger: false }; // Domain hasn't changed
      }
    }

    return { shouldTrigger: true, domain, config };
  }

  /**
   * Triggers auto-fill workflow and applies results to row values
   */
  static async triggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    domain: string,
    config: AutoFillConfig,
    session: { tenantId: string | null; userId: string | null },
    rowId?: string
  ): Promise<AutoFillResult> {
    
    try {
      // Find the auto-fill workflow
      const workflows = await WorkflowsService.getAll({ tenantId: session.tenantId });
      const autoFillWorkflow = workflows.find(w => 
        w.name === (config.workflowName || "Auto-Fill Company Data") && w.status === "live"
      );
      
      if (!autoFillWorkflow) {
        return {
          success: false,
          error: `Auto-fill workflow '${config.workflowName}' not found or not active`
        };
      }

      // Execute the workflow
      const execution = await WorkflowsExecutionsService.execute(autoFillWorkflow.id, {
        type: "manual",
        input: {
          domain,
          entityId: rowId,
          entityName: entity.name
        },
        session
      });

      if (execution.error) {
        return {
          success: false,
          error: execution.error
        };
      }

      // Parse the enriched data from workflow output
      let enrichedData: Record<string, any> = {};
      if (execution.output) {
        try {
          const output = JSON.parse(execution.output);
          enrichedData = output.enrichedData || output;
        } catch (e) {
          // If output is not JSON, skip auto-fill
          return {
            success: false,
            error: "Invalid workflow output format"
          };
        }
      }

      // Apply field mappings to row values
      const appliedMappings = this.applyFieldMappings(
        entity,
        rowValues,
        enrichedData,
        config.fieldMappings
      );

      return {
        success: true,
        enrichedData,
        appliedMappings
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || "Auto-fill failed"
      };
    }
  }

  /**
   * Applies field mappings from enriched data to row values
   */
  private static applyFieldMappings(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    enrichedData: Record<string, any>,
    fieldMappings: Record<string, string>
  ): Record<string, any> {
    
    const appliedMappings: Record<string, any> = {};

    for (const [apiField, entityField] of Object.entries(fieldMappings)) {
      const enrichedValue = enrichedData[apiField];
      if (!enrichedValue) continue;

      // Find the entity property
      const property = entity.properties.find(p => p.name === entityField);
      if (!property) continue;

      // Check if the field is already populated (don't overwrite existing data)
      const existingValue = rowValues.dynamicProperties.find(p => p.propertyId === property.id);
      if (existingValue?.textValue) continue; // Skip if already has value

      // Add or update the row value
      const existingIndex = rowValues.dynamicProperties.findIndex(p => p.propertyId === property.id);

      // Handle different data types
      let textValue: string;
      let numberValue: number | null = null;
      let booleanValue: boolean | null = null;

      if (Array.isArray(enrichedValue)) {
        // Convert arrays to comma-separated strings
        textValue = enrichedValue.join(", ");
      } else if (typeof enrichedValue === 'number') {
        textValue = String(enrichedValue);
        numberValue = enrichedValue;
      } else if (typeof enrichedValue === 'boolean') {
        textValue = String(enrichedValue);
        booleanValue = enrichedValue;
      } else {
        textValue = String(enrichedValue);
      }

      const newValue: RowValueDto = {
        propertyId: property.id,
        textValue,
        numberValue,
        dateValue: null,
        booleanValue,
        media: []
      };

      if (existingIndex >= 0) {
        rowValues.dynamicProperties[existingIndex] = newValue;
      } else {
        rowValues.dynamicProperties.push(newValue);
      }

      appliedMappings[entityField] = enrichedValue;
    }

    return appliedMappings;
  }

  /**
   * Extracts domain from URL or validates domain format
   */
  private static extractDomain(input: string): string | null {
    if (!input) return null;

    // Remove protocol and www
    let domain = input.toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .split('/')[0]
      .split('?')[0]
      .split('#')[0];

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain) ? domain : null;
  }

  /**
   * Gets auto-fill configuration for an entity (from database or creates default)
   */
  private static async getAutoFillConfig(entityName: string): Promise<AutoFillConfig | null> {
    // First check if we have a dynamic config loaded
    if (DYNAMIC_CONFIGS[entityName]) {
      return DYNAMIC_CONFIGS[entityName];
    }

    // Try to load from database
    const config = await this.loadConfigFromDatabase(entityName);
    if (config) {
      DYNAMIC_CONFIGS[entityName] = config;
      return config;
    }

    // Return null if no configuration exists
    return null;
  }

  /**
   * Creates or updates auto-fill configuration for an entity
   */
  static async setAutoFillConfig(entityName: string, config: AutoFillConfig): Promise<void> {
    DYNAMIC_CONFIGS[entityName] = config;
    await this.saveConfigToDatabase(entityName, config);
  }

  /**
   * Gets all auto-fill configurations
   */
  static getAllConfigs(): Record<string, AutoFillConfig> {
    return { ...DYNAMIC_CONFIGS };
  }

  /**
   * Loads configuration from database
   */
  private static async loadConfigFromDatabase(entityName: string): Promise<AutoFillConfig | null> {
    try {
      const { db } = await import("~/utils/db.server");

      const config = await db.autoFillConfig.findFirst({
        where: { entityName },
        include: { fieldMappings: true }
      });

      if (!config) return null;

      return {
        enabled: config.enabled,
        entityName: config.entityName,
        triggerField: config.triggerField,
        workflowName: config.workflowName || DEFAULT_CONFIG_TEMPLATE.workflowName,
        minDomainLength: config.minDomainLength || DEFAULT_CONFIG_TEMPLATE.minDomainLength,
        fieldMappings: config.fieldMappings.reduce((acc, mapping) => {
          acc[mapping.apiField] = mapping.entityField;
          return acc;
        }, {} as Record<string, string>)
      };
    } catch (error) {
      console.error("Failed to load auto-fill config from database:", error);
      return null;
    }
  }

  /**
   * Saves configuration to database
   */
  private static async saveConfigToDatabase(entityName: string, config: AutoFillConfig): Promise<void> {
    try {
      const { db } = await import("~/utils/db.server");

      // Upsert the main config
      const savedConfig = await db.autoFillConfig.upsert({
        where: { entityName },
        update: {
          enabled: config.enabled,
          triggerField: config.triggerField,
          workflowName: config.workflowName,
          minDomainLength: config.minDomainLength
        },
        create: {
          entityName: config.entityName,
          enabled: config.enabled,
          triggerField: config.triggerField,
          workflowName: config.workflowName || DEFAULT_CONFIG_TEMPLATE.workflowName,
          minDomainLength: config.minDomainLength || DEFAULT_CONFIG_TEMPLATE.minDomainLength
        }
      });

      // Delete existing field mappings
      await db.autoFillFieldMapping.deleteMany({
        where: { configId: savedConfig.id }
      });

      // Create new field mappings
      if (config.fieldMappings && Object.keys(config.fieldMappings).length > 0) {
        await db.autoFillFieldMapping.createMany({
          data: Object.entries(config.fieldMappings).map(([apiField, entityField]) => ({
            configId: savedConfig.id,
            apiField,
            entityField
          }))
        });
      }
    } catch (error) {
      console.error("Failed to save auto-fill config to database:", error);
      throw error;
    }
  }
}

export default AutoFillService;
