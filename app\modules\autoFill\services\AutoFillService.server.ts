import { EntityWithDetails } from "~/utils/db/entities/entities.db.server";
import { RowValueDto } from "~/application/dtos/entities/RowValueDto";
import WorkflowsExecutionsService from "~/modules/workflowEngine/services/WorkflowsExecutionsService";
import WorkflowsService from "~/modules/workflowEngine/services/WorkflowsService";

export interface AutoFillConfig {
  enabled: boolean;
  entityName: string;
  triggerField: string; // e.g., "website", "domain"
  workflowName?: string;
  fieldMappings: Record<string, string>; // API response field -> entity property mapping
  minDomainLength?: number;
}

export interface AutoFillResult {
  success: boolean;
  enrichedData?: Record<string, any>;
  error?: string;
  appliedMappings?: Record<string, any>;
}

// Simple configuration - works for any entity with website/domain fields
const AUTO_FILL_CONFIG = {
  enabled: true,
  workflowName: "Auto-Fill Company Data",
  minDomainLength: 3,
  // Fields that can trigger auto-fill (website, domain, url, etc.)
  triggerFields: ["website", "domain", "url", "companyWebsite", "siteUrl"],
  // Default API endpoint
  apiEndpoint: "http://localhost:7071/api/scrapagent"
};

class AutoFillService {
  
  /**
   * Checks if auto-fill should be triggered for the given entity and row values
   */
  static shouldTriggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    existingRowValues?: { dynamicProperties: RowValueDto[] }
  ): { shouldTrigger: boolean; domain?: string; triggerField?: string } {

    if (!AUTO_FILL_CONFIG.enabled) {
      return { shouldTrigger: false };
    }

    // Find any trigger field that has a value
    let triggerField: string | undefined;
    let triggerValue: string | undefined;
    let triggerProperty: any;

    for (const fieldName of AUTO_FILL_CONFIG.triggerFields) {
      triggerProperty = entity.properties.find(p => p.name === fieldName);
      if (triggerProperty) {
        const currentValue = rowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
        if (currentValue?.textValue) {
          triggerField = fieldName;
          triggerValue = currentValue.textValue;
          break;
        }
      }
    }

    if (!triggerField || !triggerValue || !triggerProperty) {
      return { shouldTrigger: false };
    }

    // Extract domain from the trigger field value
    const domain = this.extractDomain(triggerValue);
    if (!domain || domain.length < AUTO_FILL_CONFIG.minDomainLength) {
      return { shouldTrigger: false };
    }

    // Check if this is a new value (not updating with same domain)
    if (existingRowValues) {
      const existingValue = existingRowValues.dynamicProperties.find(p => p.propertyId === triggerProperty.id);
      const existingDomain = existingValue?.textValue ? this.extractDomain(existingValue.textValue) : null;

      if (existingDomain === domain) {
        return { shouldTrigger: false }; // Same domain, don't trigger
      }
    }

    return {
      shouldTrigger: true,
      domain,
      triggerField
    };
  }

  /**
   * Triggers auto-fill workflow and applies results to row values
   */
  static async triggerAutoFill(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    domain: string,
    session: { tenantId: string | null; userId: string | null },
    rowId?: string
  ): Promise<AutoFillResult> {
    
    try {
      // Find the auto-fill workflow
      const workflows = await WorkflowsService.getAll({ tenantId: session.tenantId });
      const autoFillWorkflow = workflows.find(w =>
        w.name === AUTO_FILL_CONFIG.workflowName && w.status === "live"
      );
      
      if (!autoFillWorkflow) {
        return {
          success: false,
          error: `Auto-fill workflow '${AUTO_FILL_CONFIG.workflowName}' not found or not active`
        };
      }

      // Execute the workflow
      const execution = await WorkflowsExecutionsService.execute(autoFillWorkflow.id, {
        type: "manual",
        input: {
          domain,
          entityId: rowId,
          entityName: entity.name
        },
        session
      });

      if (execution.error) {
        return {
          success: false,
          error: execution.error
        };
      }

      // Parse the enriched data from workflow output
      let enrichedData: Record<string, any> = {};
      if (execution.output) {
        try {
          const output = JSON.parse(execution.output);
          enrichedData = output.enrichedData || output;
        } catch (e) {
          // If output is not JSON, skip auto-fill
          return {
            success: false,
            error: "Invalid workflow output format"
          };
        }
      }

      // Create dynamic field mappings and apply them
      const fieldMappings = this.createFieldMappings(entity, enrichedData);
      const appliedMappings = this.applyFieldMappings(
        entity,
        rowValues,
        enrichedData,
        fieldMappings
      );

      return {
        success: true,
        enrichedData,
        appliedMappings
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || "Auto-fill failed"
      };
    }
  }

  /**
   * Applies field mappings from enriched data to row values
   */
  private static applyFieldMappings(
    entity: EntityWithDetails,
    rowValues: { dynamicProperties: RowValueDto[] },
    enrichedData: Record<string, any>,
    fieldMappings: Record<string, string>
  ): Record<string, any> {
    
    const appliedMappings: Record<string, any> = {};

    for (const [apiField, entityField] of Object.entries(fieldMappings)) {
      const enrichedValue = enrichedData[apiField];
      if (!enrichedValue) continue;

      // Find the entity property
      const property = entity.properties.find(p => p.name === entityField);
      if (!property) continue;

      // Check if the field is already populated (don't overwrite existing data)
      const existingValue = rowValues.dynamicProperties.find(p => p.propertyId === property.id);
      if (existingValue?.textValue) continue; // Skip if already has value

      // Add or update the row value
      const existingIndex = rowValues.dynamicProperties.findIndex(p => p.propertyId === property.id);

      // Handle different data types
      let textValue: string;
      let numberValue: number | null = null;
      let booleanValue: boolean | null = null;

      if (Array.isArray(enrichedValue)) {
        // Convert arrays to comma-separated strings
        textValue = enrichedValue.join(", ");
      } else if (typeof enrichedValue === 'number') {
        textValue = String(enrichedValue);
        numberValue = enrichedValue;
      } else if (typeof enrichedValue === 'boolean') {
        textValue = String(enrichedValue);
        booleanValue = enrichedValue;
      } else {
        textValue = String(enrichedValue);
      }

      const newValue: RowValueDto = {
        propertyId: property.id,
        textValue,
        numberValue,
        dateValue: null,
        booleanValue,
        media: []
      };

      if (existingIndex >= 0) {
        rowValues.dynamicProperties[existingIndex] = newValue;
      } else {
        rowValues.dynamicProperties.push(newValue);
      }

      appliedMappings[entityField] = enrichedValue;
    }

    return appliedMappings;
  }

  /**
   * Extracts domain from URL or validates domain format
   */
  private static extractDomain(input: string): string | null {
    if (!input) return null;

    // Remove protocol and www
    let domain = input.toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .split('/')[0]
      .split('?')[0]
      .split('#')[0];

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    return domainRegex.test(domain) ? domain : null;
  }

  /**
   * Creates field mappings dynamically based on entity properties and API response
   */
  static createFieldMappings(entity: EntityWithDetails, apiResponse: any): Record<string, string> {
    const mappings: Record<string, string> = {};

    // Get all entity properties that can be auto-filled
    const fillableProperties = entity.properties.filter(p =>
      p.type === "text" || p.type === "number" || p.type === "select"
    );

    // Try to match API response fields to entity properties
    for (const property of fillableProperties) {
      const propertyName = property.name.toLowerCase();

      // Try exact matches first
      if (apiResponse[propertyName] !== undefined) {
        mappings[propertyName] = property.name;
        continue;
      }

      // Try common field name variations
      const variations = this.getFieldVariations(propertyName);
      for (const variation of variations) {
        if (apiResponse[variation] !== undefined) {
          mappings[variation] = property.name;
          break;
        }
      }

      // Try nested object matching
      for (const [key, value] of Object.entries(apiResponse)) {
        if (typeof value === 'object' && value !== null) {
          for (const nestedKey of Object.keys(value)) {
            if (nestedKey.toLowerCase().includes(propertyName) ||
                propertyName.includes(nestedKey.toLowerCase())) {
              mappings[`${key}.${nestedKey}`] = property.name;
              break;
            }
          }
        }
      }
    }

    return mappings;
  }

  /**
   * Gets common variations of a field name for matching
   */
  private static getFieldVariations(fieldName: string): string[] {
    const variations = [fieldName];

    // Common field name mappings
    const commonMappings: Record<string, string[]> = {
      'name': ['company_name', 'companyName', 'title', 'organization'],
      'website': ['url', 'site', 'domain', 'web_url'],
      'description': ['about', 'summary', 'overview', 'bio'],
      'phone': ['telephone', 'contact', 'phoneNumber'],
      'address': ['location', 'street', 'addr'],
      'employees': ['size', 'team_size', 'staff_count'],
      'industry': ['sector', 'business_type', 'category'],
      'founded': ['established', 'founding_year', 'start_date']
    };

    if (commonMappings[fieldName]) {
      variations.push(...commonMappings[fieldName]);
    }

    return variations;
  }
}

export default AutoFillService;
