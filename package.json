{"name": "linkworks", "version": "1.6.1", "private": true, "sideEffects": false, "type": "module", "prisma": {"schema": "./prisma/schema", "seed": "tsx prisma/seed.ts"}, "lint-staged": {"**/*.{js,jsx,ts,tsx,css,md,json}": "prettier --write", "**/*.{ts,tsx}": "eslint --fix"}, "scripts": {"dev": "react-router dev", "build": "prisma generate && react-router build", "lint": "eslint --ext .tsx,.ts .", "test": "vitest app/", "typecheck": "react-router typegen && tsc -p tsconfig.json --pretty", "prettier": "prettier --write '**/*.{ts,tsx}'", "start": "react-router-serve ./build/server/index.js", "seed": "tsx prisma/seed.ts", "seed:prod": "NODE_ENV=production tsx prisma/seed.ts", "pre-commit": "lint-staged --allow-empty --concurrent false", "prepare": "husky install"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/lib-storage": "^3.817.0", "@epic-web/cachified": "^4.0.0", "@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@monaco-editor/react": "^4.6.0", "@novu/node": "^0.24.2", "@novu/react": "^2.6.6", "@popperjs/core": "^2.11.8", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-router/node": "^7.2.0", "@react-router/remix-config-routes-adapter": "^0.0.0-nightly-bf7ecb711-20240911", "@react-router/serve": "^7.2.0", "@remix-run/server-runtime": "^2.15.1", "@sendgrid/mail": "^8.1.4", "@sindresorhus/slugify": "^2.2.1", "@stripe/stripe-js": "^1.35.0", "@supabase/supabase-js": "^2.26.0", "@tiptap/core": "^2.1.13", "@tiptap/extension-color": "^2.1.13", "@tiptap/extension-horizontal-rule": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-placeholder": "^2.0.3", "@tiptap/extension-text-style": "^2.1.13", "@tiptap/extension-underline": "^2.1.13", "@tiptap/pm": "^2.1.13", "@tiptap/react": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/suggestion": "^2.1.13", "@tremor/react": "^3.17.2", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.4.0", "ajv": "^8.12.0", "bcryptjs": "^2.4.3", "browser-image-compression": "^1.0.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "company-email-validator": "^1.0.7", "crypto-js": "^4.2.0", "dagre": "^0.8.5", "date-fns": "^3.6.0", "decimal.js": "^10.4.3", "embla-carousel-react": "^8.5.2", "eventsource-parser": "^1.0.0", "framer-motion": "^11.14.4", "handlebars": "^4.7.8", "highlight.js": "^11.9.0", "html-to-text": "^9.0.5", "i18next": "^23.16.3", "i18next-browser-languagedetector": "^7.1.0", "i18next-fs-backend": "^2.2.0", "i18next-http-backend": "^2.2.2", "input-otp": "^1.4.2", "is-ip": "^3.1.0", "isbot": "^3.7.0", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.0", "kbar": "^0.1.0-beta.45", "lru-cache": "^10.1.0", "lucide-react": "^0.482.0", "mailchecker": "^6.0.1", "marked": "^4.0.14", "moment": "^2.29.1", "motion": "^12.5.0", "nanoid": "^3.3.6", "next-themes": "^0.2.1", "numeral": "^2.0.6", "openai": "^4.47.2", "platform": "^1.3.6", "postmark": "^3.11.0", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-confetti": "^6.1.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-google-recaptcha": "^2.1.0", "react-google-recaptcha-ultimate": "^1.2.2", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "react-i18next": "^15.1.0", "react-json-tree": "^0.17.0", "react-resizable-panels": "^2.1.7", "react-router": "^7.2.0", "react-sortablejs": "^6.1.4", "react-use-measure": "^2.1.7", "reactflow": "^11.10.1", "recharts": "^2.15.1", "rehype-highlight": "^5.0.2", "remix-auth": "^3.6.0", "remix-auth-form": "^1.4.0", "remix-auth-google": "^2.0.0", "remix-i18next": "^7.0.1", "resend": "^2.0.0", "sonner": "^1.7.4", "sortablejs": "^1.15.0", "spin-delay": "^1.1.0", "stripe": "^13.11.0", "swagger-ui-dist": "^4.19.0", "swr": "^2.2.0", "tailwind-merge": "^3.0.2", "tiny-invariant": "^1.3.1", "tippy.js": "^6.3.7", "use-debounce": "^9.0.3", "uuid": "^9.0.1", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@react-router/dev": "^7.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.9", "@testing-library/cypress": "^8.0.2", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^14.1.2", "@types/adm-zip": "^0.5.5", "@types/bcryptjs": "^2.4.2", "@types/crypto-js": "^4.2.1", "@types/dagre": "^0.7.52", "@types/html-to-text": "^9.0.1", "@types/json2csv": "^5.0.3", "@types/jsonwebtoken": "^9.0.6", "@types/marked": "^4.0.3", "@types/numeral": "^2.0.2", "@types/platform": "^1.3.4", "@types/postmark": "^2.0.3", "@types/react": "^19.0.1", "@types/react-beautiful-dnd": "^13.1.4", "@types/react-command-palette": "^0.18.0", "@types/react-datepicker": "^4.3.4", "@types/react-dom": "^19.0.2", "@types/react-google-recaptcha": "^2.1.5", "@types/react-table": "^7.7.11", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "cross-env": "^7.0.3", "eslint": "^8.54.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "husky": "^8.0.0", "jsdom": "^23.0.1", "lint-staged": "^15.5.0", "minimatch": "^10.0.1", "postcss": "^8.4.32", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.5.0", "tailwindcss": "^4.0.14", "tailwindcss-animate": "^1.0.7", "tsx": "^4.6.2", "typescript": "^5.3.2", "vite": "^5.0.10", "vite-tsconfig-paths": "^4.2.2", "vitest": "^0.29.8"}}